#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للتحسينات المطبقة على تطبيق Modetaris
"""

import os
import sys
import subprocess
import time

def test_javascript_improvements():
    """اختبار التحسينات في JavaScript"""
    print("🧪 اختبار التحسينات في JavaScript...")
    
    js_file = "modetaris/app/src/main/assets/script.js"
    
    if not os.path.exists(js_file):
        print(f"❌ ملف JavaScript غير موجود: {js_file}")
        return False
    
    with open(js_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود التحسينات المطلوبة
    improvements = [
        ("معالجة روابط Firebase", "firebasestorage.googleapis.com"),
        ("نظام الروابط البديلة", "generateDownloadAlternatives"),
        ("التحقق من صحة الروابط", "validateFirebaseLink"),
        ("نظام إعادة المحاولة", "downloadModFileWithFallback"),
        ("معالجة أخطاء التحميل", "showDownloadErrorMessage"),
        ("التحقق من الشبكة", "checkNetworkConnectivity"),
        ("تحسين معالجة الروابط", "processedLink")
    ]
    
    passed = 0
    for name, keyword in improvements:
        if keyword in content:
            print(f"  ✅ {name}: موجود")
            passed += 1
        else:
            print(f"  ❌ {name}: غير موجود")
    
    print(f"📊 نتائج JavaScript: {passed}/{len(improvements)} تحسينات موجودة")
    return passed == len(improvements)

def test_kotlin_improvements():
    """اختبار التحسينات في Kotlin"""
    print("\n🧪 اختبار التحسينات في Kotlin...")
    
    kotlin_file = "modetaris/app/src/main/java/com/sidimohamed/modetaris/MainActivity.kt"
    
    if not os.path.exists(kotlin_file):
        print(f"❌ ملف Kotlin غير موجود: {kotlin_file}")
        return False
    
    with open(kotlin_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود التحسينات المطلوبة
    improvements = [
        ("تحسين روابط Firebase", "optimizeFirebaseUrl"),
        ("معالجة أخطاء التحميل", "getDownloadFailureReason"),
        ("نظام إعادة المحاولة", "retryDownloadWithAlternativeUrl"),
        ("إنشاء روابط بديلة", "generateAlternativeFirebaseUrl"),
        ("تحديد إعادة المحاولة", "shouldRetryDownload"),
        ("معالجة Firebase Storage", "firebasestorage.googleapis.com"),
        ("معالجة Google Storage", "storage.googleapis.com")
    ]
    
    passed = 0
    for name, keyword in improvements:
        if keyword in content:
            print(f"  ✅ {name}: موجود")
            passed += 1
        else:
            print(f"  ❌ {name}: غير موجود")
    
    print(f"📊 نتائج Kotlin: {passed}/{len(improvements)} تحسينات موجودة")
    return passed == len(improvements)

def test_build_compatibility():
    """اختبار توافق البناء"""
    print("\n🧪 اختبار توافق البناء...")
    
    try:
        # التحقق من وجود ملفات البناء المطلوبة
        build_files = [
            "modetaris/app/build.gradle.kts",
            "modetaris/app/src/main/AndroidManifest.xml",
            "modetaris/app/src/main/res/values/strings.xml"
        ]
        
        missing_files = []
        for file_path in build_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ ملفات مفقودة: {missing_files}")
            return False
        
        print("✅ جميع ملفات البناء موجودة")
        
        # محاولة فحص syntax للملفات الرئيسية
        print("🔍 فحص syntax للملفات...")
        
        # فحص JavaScript
        js_file = "modetaris/app/src/main/assets/script.js"
        if os.path.exists(js_file):
            # فحص بسيط للـ syntax errors
            with open(js_file, 'r', encoding='utf-8') as f:
                js_content = f.read()
            
            # التحقق من توازن الأقواس
            if js_content.count('{') != js_content.count('}'):
                print("❌ خطأ في توازن الأقواس المجعدة في JavaScript")
                return False
            
            if js_content.count('(') != js_content.count(')'):
                print("❌ خطأ في توازن الأقواس العادية في JavaScript")
                return False
            
            print("✅ JavaScript syntax يبدو صحيحاً")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البناء: {e}")
        return False

def test_firebase_url_processing():
    """اختبار معالجة روابط Firebase"""
    print("\n🧪 اختبار معالجة روابط Firebase...")
    
    test_urls = [
        {
            "input": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2Ftest.mcpack?alt=media",
            "expected_type": "firebase",
            "should_work": True
        },
        {
            "input": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/test.mcpack",
            "expected_type": "storage",
            "should_work": True
        },
        {
            "input": "https://mwxzwfeqsashcwvqthmd.supabase.co/storage/v1/object/public/mods/test.mcpack",
            "expected_type": "supabase",
            "should_work": True
        },
        {
            "input": "invalid-url",
            "expected_type": "invalid",
            "should_work": False
        }
    ]
    
    passed = 0
    for i, test_case in enumerate(test_urls):
        url = test_case["input"]
        expected_type = test_case["expected_type"]
        should_work = test_case["should_work"]
        
        print(f"  اختبار {i+1}: {expected_type}")
        
        # فحص تنسيق الرابط
        if should_work:
            if url.startswith('http'):
                print(f"    ✅ تنسيق الرابط صحيح")
                passed += 1
            else:
                print(f"    ❌ تنسيق الرابط خاطئ")
        else:
            if not url.startswith('http'):
                print(f"    ✅ تم اكتشاف الرابط غير الصحيح")
                passed += 1
            else:
                print(f"    ❌ لم يتم اكتشاف الرابط غير الصحيح")
    
    print(f"📊 نتائج معالجة الروابط: {passed}/{len(test_urls)} اختبارات نجحت")
    return passed == len(test_urls)

def generate_test_report():
    """إنشاء تقرير شامل للاختبارات"""
    print("\n" + "="*60)
    print("📋 تقرير شامل لاختبار تحسينات تطبيق Modetaris")
    print("="*60)
    
    tests = [
        ("تحسينات JavaScript", test_javascript_improvements),
        ("تحسينات Kotlin", test_kotlin_improvements),
        ("توافق البناء", test_build_compatibility),
        ("معالجة روابط Firebase", test_firebase_url_processing)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed_tests += 1
                print(f"\n✅ {test_name}: نجح")
            else:
                print(f"\n❌ {test_name}: فشل")
        except Exception as e:
            print(f"\n❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*60)
    print(f"📊 النتائج النهائية: {passed_tests}/{total_tests} اختبارات نجحت")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام.")
        print("\n✅ التحسينات المطبقة:")
        print("   • تحسين معالجة روابط Firebase")
        print("   • نظام روابط بديلة للتحميل")
        print("   • معالجة محسنة لأخطاء التحميل")
        print("   • نظام إعادة المحاولة التلقائي")
        print("   • التحقق من صحة الروابط")
        print("   • مراقبة حالة الشبكة")
        print("   • واجهة مستخدم محسنة للأخطاء")
        
        print("\n💡 نصائح للاستخدام:")
        print("   • تأكد من اتصال إنترنت مستقر")
        print("   • امنح التطبيق صلاحيات التخزين")
        print("   • تحقق من مساحة التخزين المتاحة")
        print("   • استخدم Wi-Fi للملفات الكبيرة")
        
        return True
    else:
        print("❌ بعض الاختبارات فشلت. راجع التفاصيل أعلاه.")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار تحسينات تطبيق Modetaris")
    print("تاريخ الاختبار:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    success = generate_test_report()
    
    if success:
        print("\n🎯 التطبيق جاهز لحل مشكلة روابط Firebase!")
        return 0
    else:
        print("\n⚠️ يحتاج التطبيق إلى مراجعة إضافية.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
