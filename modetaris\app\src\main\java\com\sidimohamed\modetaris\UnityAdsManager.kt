package com.sidimohamed.modetaris

import android.app.Activity
import android.content.Context
import android.util.Log
import com.unity3d.ads.IUnityAdsInitializationListener
import com.unity3d.ads.IUnityAdsLoadListener
import com.unity3d.ads.IUnityAdsShowListener
import com.unity3d.ads.UnityAds
import com.unity3d.ads.UnityAds.UnityAdsShowError

/**
 * Unity Ads Manager class to handle Unity Ads SDK initialization and rewarded ads
 */
class UnityAdsManager(private val context: Context) {
    
    companion object {
        private const val TAG = "UnityAdsManager"

        // Unity Ads Game IDs - Updated with correct IDs
        private const val ANDROID_GAME_ID = "5916047"
        private const val IOS_GAME_ID = "5916047" // Using the same for simplicity, adjust if different

        // Unity Ads Placement IDs - Using standard placement names
        private const val REWARDED_PLACEMENT_ID = "gift"

        // Test mode - set to false for production to show real ads
        private const val TEST_MODE = false
    }
    
    private var isInitialized = false
    private var isRewardedAdLoaded = false
    private var onRewardedAdCompleted: (() -> Unit)? = null
    private var onRewardedAdFailed: ((String) -> Unit)? = null
    private var loadAttempts = 0
    private val maxLoadAttempts = 3
    
    /**
     * Initialize Unity Ads SDK
     */
    fun initialize(onInitializationComplete: ((Boolean) -> Unit)? = null) {
        if (isInitialized) {
            Log.d(TAG, "Unity Ads already initialized")
            onInitializationComplete?.invoke(true)
            return
        }

        Log.i(TAG, "Initializing Unity Ads SDK with Game ID: $ANDROID_GAME_ID, Test Mode: $TEST_MODE")

        UnityAds.initialize(
            context,
            ANDROID_GAME_ID,
            TEST_MODE,
            object : IUnityAdsInitializationListener {
                override fun onInitializationComplete() {
                    Log.i(TAG, "✅ Unity Ads initialization completed successfully!")
                    isInitialized = true
                    onInitializationComplete?.invoke(true)

                    // Load rewarded ad after initialization
                    Log.d(TAG, "Starting to load rewarded ad after initialization...")
                    loadRewardedAd()
                }

                override fun onInitializationFailed(
                    error: UnityAds.UnityAdsInitializationError?,
                    message: String?
                ) {
                    Log.e(TAG, "❌ Unity Ads initialization failed!")
                    Log.e(TAG, "Error: $error")
                    Log.e(TAG, "Message: $message")
                    isInitialized = false
                    onInitializationComplete?.invoke(false)
                }
            }
        )
    }
    
    /**
     * Load rewarded ad with improved error handling and retry logic
     */
    fun loadRewardedAd() {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ Unity Ads not initialized, cannot load rewarded ad")
            return
        }

        if (isRewardedAdLoaded) {
            Log.d(TAG, "Rewarded ad already loaded.")
            return
        }

        loadAttempts++
        Log.i(TAG, "🔄 Loading rewarded ad (attempt $loadAttempts/$maxLoadAttempts) with placement: $REWARDED_PLACEMENT_ID")

        UnityAds.load(REWARDED_PLACEMENT_ID, object : IUnityAdsLoadListener {
            override fun onUnityAdsAdLoaded(placementId: String?) {
                Log.i(TAG, "✅ Rewarded ad loaded successfully for placement: $placementId")
                isRewardedAdLoaded = true
                loadAttempts = 0 // Reset attempts on success
            }

            override fun onUnityAdsFailedToLoad(
                placementId: String?,
                error: UnityAds.UnityAdsLoadError?,
                message: String?
            ) {
                Log.e(TAG, "❌ Failed to load rewarded ad for placement $placementId. Error: $error, Message: $message")
                isRewardedAdLoaded = false

                if (loadAttempts < maxLoadAttempts) {
                    // Retry after a delay
                    val retryDelay = 5000L // 5 seconds
                    Log.d(TAG, "Retrying ad load in ${retryDelay / 1000} seconds...")
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        loadRewardedAd()
                    }, retryDelay)
                } else {
                    Log.e(TAG, "❌ Max load attempts reached. Giving up for now.")
                    loadAttempts = 0 // Reset for next time
                }
            }
        })
    }
    
    /**
     * Show rewarded ad with improved error handling
     */
    fun showRewardedAd(
        activity: Activity,
        onCompleted: () -> Unit,
        onFailed: (String) -> Unit
    ) {
        Log.i(TAG, "🎬 Attempting to show rewarded ad...")
        Log.d(TAG, "Initialized: $isInitialized, Ad Loaded: $isRewardedAdLoaded, Placement: $REWARDED_PLACEMENT_ID")

        if (!isInitialized) {
            Log.w(TAG, "⚠️ Unity Ads not initialized")
            onFailed("Unity Ads not initialized")
            return
        }

        if (!isRewardedAdLoaded) {
            Log.w(TAG, "⚠️ Rewarded ad not loaded, trying to load and show...")
            // Store callbacks for when ad loads
            onRewardedAdCompleted = onCompleted
            onRewardedAdFailed = onFailed

            // Try to load ad and show when ready
            loadRewardedAdAndShow(activity)
            return
        }

        // Store callbacks
        onRewardedAdCompleted = onCompleted
        onRewardedAdFailed = onFailed

        Log.i(TAG, "🎬 Showing rewarded ad with placement: $REWARDED_PLACEMENT_ID")

        UnityAds.show(activity, REWARDED_PLACEMENT_ID, object : IUnityAdsShowListener {
            override fun onUnityAdsShowStart(placementId: String?) {
                Log.i(TAG, "🎬 Rewarded ad show started for placement: $placementId")
            }

            override fun onUnityAdsShowClick(placementId: String?) {
                Log.i(TAG, "👆 Rewarded ad clicked for placement: $placementId")
            }

            override fun onUnityAdsShowComplete(
                placementId: String?,
                state: UnityAds.UnityAdsShowCompletionState?
            ) {
                Log.i(TAG, "🏁 Rewarded ad show completed for placement: $placementId with state: $state")

                when (state) {
                    UnityAds.UnityAdsShowCompletionState.COMPLETED -> {
                        Log.i(TAG, "✅ User watched the complete rewarded ad - REWARD GRANTED!")
                        onRewardedAdCompleted?.invoke()
                    }
                    UnityAds.UnityAdsShowCompletionState.SKIPPED -> {
                        Log.w(TAG, "⏭️ User skipped the rewarded ad")
                        onRewardedAdFailed?.invoke("Ad was skipped")
                    }
                    else -> {
                        Log.w(TAG, "❓ Rewarded ad completed with unknown state: $state")
                        onRewardedAdFailed?.invoke("Ad completed with unknown state")
                    }
                }

                // Load next ad
                isRewardedAdLoaded = false
                Log.d(TAG, "🔄 Loading next ad after completion...")
                loadRewardedAd()

                // Clear callbacks
                onRewardedAdCompleted = null
                onRewardedAdFailed = null
            }

            override fun onUnityAdsShowFailure(
                placementId: String?,
                error: UnityAdsShowError?,
                message: String?
            ) {
                Log.e(TAG, "❌ Failed to show rewarded ad for placement $placementId")
                Log.e(TAG, "Error: $error")
                Log.e(TAG, "Message: $message")
                onRewardedAdFailed?.invoke("Failed to show ad: $message")

                // Load next ad
                isRewardedAdLoaded = false
                Log.d(TAG, "🔄 Loading next ad after failure...")
                loadRewardedAd()

                // Clear callbacks
                onRewardedAdCompleted = null
                onRewardedAdFailed = null
            }
        })
    }

    /**
     * Load rewarded ad and show when ready
     */
    private fun loadRewardedAdAndShow(activity: Activity) {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ Unity Ads not initialized, cannot load rewarded ad")
            onRewardedAdFailed?.invoke("Unity Ads not initialized")
            return
        }

        Log.i(TAG, "🔄 Loading rewarded ad to show immediately when ready...")

        UnityAds.load(REWARDED_PLACEMENT_ID, object : IUnityAdsLoadListener {
            override fun onUnityAdsAdLoaded(placementId: String?) {
                Log.i(TAG, "✅ Rewarded ad loaded successfully, showing now...")
                isRewardedAdLoaded = true

                // Show the ad immediately
                UnityAds.show(activity, REWARDED_PLACEMENT_ID, object : IUnityAdsShowListener {
                    override fun onUnityAdsShowStart(placementId: String?) {
                        Log.i(TAG, "🎬 Rewarded ad show started for placement: $placementId")
                    }

                    override fun onUnityAdsShowClick(placementId: String?) {
                        Log.i(TAG, "👆 Rewarded ad clicked for placement: $placementId")
                    }

                    override fun onUnityAdsShowComplete(
                        placementId: String?,
                        state: UnityAds.UnityAdsShowCompletionState?
                    ) {
                        Log.i(TAG, "🏁 Rewarded ad show completed for placement: $placementId with state: $state")

                        when (state) {
                            UnityAds.UnityAdsShowCompletionState.COMPLETED -> {
                                Log.i(TAG, "✅ User watched the complete rewarded ad - REWARD GRANTED!")
                                onRewardedAdCompleted?.invoke()
                            }
                            UnityAds.UnityAdsShowCompletionState.SKIPPED -> {
                                Log.w(TAG, "⏭️ User skipped the rewarded ad")
                                onRewardedAdFailed?.invoke("Ad was skipped")
                            }
                            else -> {
                                Log.w(TAG, "❓ Rewarded ad completed with unknown state: $state")
                                onRewardedAdFailed?.invoke("Ad completed with unknown state")
                            }
                        }

                        // Load next ad and clear callbacks
                        isRewardedAdLoaded = false
                        Log.d(TAG, "🔄 Loading next ad after completion...")
                        loadRewardedAd()
                        onRewardedAdCompleted = null
                        onRewardedAdFailed = null
                    }

                    override fun onUnityAdsShowFailure(
                        placementId: String?,
                        error: UnityAds.UnityAdsShowError?,
                        message: String?
                    ) {
                        Log.e(TAG, "❌ Failed to show rewarded ad for placement $placementId")
                        Log.e(TAG, "Error: $error")
                        Log.e(TAG, "Message: $message")
                        onRewardedAdFailed?.invoke("Failed to show ad: $message")

                        // Load next ad and clear callbacks
                        isRewardedAdLoaded = false
                        Log.d(TAG, "🔄 Loading next ad after failure...")
                        loadRewardedAd()
                        onRewardedAdCompleted = null
                        onRewardedAdFailed = null
                    }
                })
            }

            override fun onUnityAdsFailedToLoad(
                placementId: String?,
                error: UnityAds.UnityAdsLoadError?,
                message: String?
            ) {
                Log.e(TAG, "❌ Failed to load rewarded ad for immediate show")
                Log.e(TAG, "Error: $error")
                Log.e(TAG, "Message: $message")

                isRewardedAdLoaded = false
                onRewardedAdFailed?.invoke("Failed to load ad: $message")

                // Clear callbacks
                onRewardedAdCompleted = null
                onRewardedAdFailed = null
            }
        })
    }

    /**
     * Check if rewarded ad is ready to show
     */
    fun isRewardedAdReady(): Boolean {
        val ready = isInitialized && isRewardedAdLoaded
        Log.d(TAG, "🔍 Ad ready check: Initialized=$isInitialized, Loaded=$isRewardedAdLoaded, Ready=$ready")
        return ready
    }

    /**
     * Get initialization status
     */
    fun isInitialized(): Boolean {
        return isInitialized
    }

    /**
     * Force reload ad (useful for debugging)
     */
    fun forceReloadAd() {
        Log.i(TAG, "🔄 Force reloading ad...")
        isRewardedAdLoaded = false
        loadAttempts = 0
        loadRewardedAd()
    }

    /**
     * Get current status for debugging with detailed information
     */
    fun getStatus(): String {
        return "Unity Ads Status: Initialized=$isInitialized, AdLoaded=$isRewardedAdLoaded, " +
                "Placement=$REWARDED_PLACEMENT_ID, Attempts=$loadAttempts, TestMode=$TEST_MODE, " +
                "GameID=$ANDROID_GAME_ID"
    }

    /**
     * Get detailed diagnostic information
     */
    fun getDiagnosticInfo(): String {
        val sb = StringBuilder()
        sb.appendLine("=== Unity Ads Diagnostic Information ===")
        sb.appendLine("SDK Version: 4.9.2")
        sb.appendLine("Game ID: $ANDROID_GAME_ID")
        sb.appendLine("Test Mode: $TEST_MODE")
        sb.appendLine("Initialized: $isInitialized")
        sb.appendLine("Ad Loaded: $isRewardedAdLoaded")
        sb.appendLine("Current Placement: $REWARDED_PLACEMENT_ID")
        sb.appendLine("Load Attempts: $loadAttempts/$maxLoadAttempts")
        sb.appendLine("Unity Ads Ready: ${isRewardedAdReady()}")
        sb.appendLine("=========================================")
        return sb.toString()
    }

    /**
     * Log detailed diagnostic information
     */
    fun logDiagnosticInfo() {
        Log.i(TAG, getDiagnosticInfo())
    }
}
