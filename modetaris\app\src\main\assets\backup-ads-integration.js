// Backup Ads Integration JavaScript
// تكامل الإعلانات الاحتياطية مع التطبيق الرئيسي

// Global variables for backup ads
let backupAdsEnabled = true;
let currentBackupAd = null;
let backupAdDisplayed = false;
let backupAdsSupabaseClient = null;

// Wait for supabaseManager to be available
function waitForSupabaseManager(timeout = 10000) {
    return new Promise((resolve, reject) => {
        if (typeof window.supabaseManager !== 'undefined' && window.supabaseManager.getClient) {
            resolve(true);
            return;
        }

        const startTime = Date.now();
        const checkInterval = setInterval(() => {
            if (typeof window.supabaseManager !== 'undefined' && window.supabaseManager.getClient) {
                clearInterval(checkInterval);
                resolve(true);
            } else if (Date.now() - startTime > timeout) {
                clearInterval(checkInterval);
                reject(new Error('Timeout waiting for supabaseManager'));
            }
        }, 100);
    });
}

// Initialize backup ads system
async function initializeBackupAds() {
    try {
        // Wait for supabaseManager to be available
        await waitForSupabaseManager();

        // Initialize Supabase client if not already done
        if (!backupAdsSupabaseClient && window.supabaseManager && window.supabaseManager.getClient) {
            backupAdsSupabaseClient = window.supabaseManager.getClient();
        }

        console.log('✅ تم تهيئة نظام الإعلانات الاحتياطية');
        return true;
    } catch (error) {
        console.error('❌ خطأ في تهيئة نظام الإعلانات الاحتياطية:', error);
        return false;
    }
}

// Show backup ad when Unity Ads fails
async function showBackupAdOnUnityAdsFailure(modId = null, modCategory = null) {
    try {
        if (!backupAdsEnabled || backupAdDisplayed) {
            console.log('نظام الإعلانات الاحتياطية معطل أو تم عرض إعلان بالفعل');
            return false;
        }
        
        // Get random backup ad
        const backupAd = await getRandomBackupAd('image', modCategory);
        if (!backupAd) {
            console.log('لا توجد إعلانات احتياطية متاحة');
            return false;
        }
        
        currentBackupAd = backupAd;
        
        // Display the backup ad
        displayBackupAd(backupAd, modId, modCategory);
        
        // Log view event
        await logBackupAdEvent(backupAd.id, 'view', modId, modCategory);
        
        backupAdDisplayed = true;
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في عرض الإعلان الاحتياطي:', error);
        return false;
    }
}

// Get random backup ad from database
async function getRandomBackupAd(adType = null, modCategory = null) {
    try {
        if (!backupAdsSupabaseClient) {
            console.error('عميل Supabase غير متاح');
            return null;
        }

        let query = backupAdsSupabaseClient
            .from('backup_ads')
            .select('*')
            .eq('is_active', true);
        
        // Filter by ad type if specified
        if (adType) {
            query = query.eq('ad_type', adType);
        }
        
        // Filter by category if specified and ad has target categories
        if (modCategory) {
            query = query.or(`target_categories.is.null,target_categories.cs.{${modCategory}}`);
        }
        
        // Filter by date range
        const now = new Date().toISOString();
        query = query
            .or(`start_date.is.null,start_date.lte.${now}`)
            .or(`end_date.is.null,end_date.gte.${now}`);
        
        const { data, error } = await query
            .order('priority', { ascending: false })
            .limit(10); // Get top 10 by priority, then randomize
        
        if (error) throw error;
        
        if (!data || data.length === 0) {
            return null;
        }
        
        // Randomly select from the results
        const randomIndex = Math.floor(Math.random() * data.length);
        return data[randomIndex];
        
    } catch (error) {
        console.error('❌ خطأ في جلب الإعلان الاحتياطي:', error);
        return null;
    }
}

// Display backup ad in modal
function displayBackupAd(ad, modId = null, modCategory = null) {
    try {
        // Create modal HTML
        const modalHTML = `
            <div id="backupAdModal" class="backup-ad-modal">
                <div class="backup-ad-overlay" onclick="closeBackupAd()"></div>
                <div class="backup-ad-container">
                    <div class="backup-ad-header">
                        <h3>${ad.title}</h3>
                        <button class="backup-ad-close" onclick="closeBackupAd()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="backup-ad-content">
                        ${ad.ad_type === 'image' 
                            ? `<img src="${ad.media_url}" alt="${ad.title}" class="backup-ad-media">`
                            : `<video src="${ad.media_url}" class="backup-ad-media" autoplay muted loop></video>`
                        }
                        ${ad.description ? `<p class="backup-ad-description">${ad.description}</p>` : ''}
                    </div>
                    <div class="backup-ad-actions">
                        ${ad.click_action === 'url' && ad.click_url 
                            ? `<button class="backup-ad-btn primary" onclick="handleBackupAdClick('${ad.id}', '${ad.click_url}', '${modId}', '${modCategory}')">
                                <i class="fas fa-external-link-alt"></i> زيارة الرابط
                               </button>`
                            : ''
                        }
                        <button class="backup-ad-btn secondary" onclick="closeBackupAd()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                    <div class="backup-ad-timer">
                        <div class="timer-bar">
                            <div class="timer-fill" id="backupAdTimerFill"></div>
                        </div>
                        <span id="backupAdTimerText">${ad.duration}</span>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Add CSS if not already added
        addBackupAdStyles();
        
        // Start timer
        startBackupAdTimer(ad.duration);
        
        console.log(`✅ تم عرض الإعلان الاحتياطي: ${ad.title}`);
        
    } catch (error) {
        console.error('❌ خطأ في عرض الإعلان الاحتياطي:', error);
    }
}

// Handle backup ad click
async function handleBackupAdClick(adId, clickUrl, modId = null, modCategory = null) {
    try {
        // Log click event
        await logBackupAdEvent(adId, 'click', modId, modCategory);
        
        // Open URL
        if (clickUrl) {
            window.open(clickUrl, '_blank');
        }
        
        // Close ad after click
        setTimeout(() => {
            closeBackupAd();
        }, 500);
        
    } catch (error) {
        console.error('❌ خطأ في معالجة نقرة الإعلان الاحتياطي:', error);
    }
}

// Close backup ad
async function closeBackupAd() {
    try {
        const modal = document.getElementById('backupAdModal');
        if (modal) {
            // Log close event if ad was displayed
            if (currentBackupAd) {
                await logBackupAdEvent(currentBackupAd.id, 'close');
            }
            
            modal.remove();
            currentBackupAd = null;
            backupAdDisplayed = false;
            
            console.log('✅ تم إغلاق الإعلان الاحتياطي');
        }
    } catch (error) {
        console.error('❌ خطأ في إغلاق الإعلان الاحتياطي:', error);
    }
}

// Start backup ad timer
function startBackupAdTimer(duration) {
    let timeLeft = duration;
    const timerFill = document.getElementById('backupAdTimerFill');
    const timerText = document.getElementById('backupAdTimerText');
    
    const timer = setInterval(() => {
        timeLeft--;
        
        if (timerFill && timerText) {
            const percentage = ((duration - timeLeft) / duration) * 100;
            timerFill.style.width = percentage + '%';
            timerText.textContent = timeLeft;
        }
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            closeBackupAd();
        }
    }, 1000);
}

// Log backup ad event
async function logBackupAdEvent(adId, eventType, modId = null, modCategory = null) {
    try {
        if (!backupAdsSupabaseClient) return;
        
        const eventData = {
            ad_id: adId,
            event_type: eventType,
            user_id: localStorage.getItem('userId') || null,
            session_id: getSessionId(),
            mod_id: modId,
            mod_category: modCategory,
            user_agent: navigator.userAgent,
            metadata: {
                timestamp: new Date().toISOString(),
                page_url: window.location.href,
                screen_resolution: `${screen.width}x${screen.height}`,
                viewport_size: `${window.innerWidth}x${window.innerHeight}`
            }
        };
        
        const { error } = await backupAdsSupabaseClient
            .from('backup_ads_stats')
            .insert([eventData]);
        
        if (error) {
            console.error('خطأ في تسجيل إحصائية الإعلان الاحتياطي:', error);
        }
        
    } catch (error) {
        console.error('❌ خطأ في تسجيل حدث الإعلان الاحتياطي:', error);
    }
}

// Get or generate session ID
function getSessionId() {
    let sessionId = sessionStorage.getItem('sessionId');
    if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
        sessionStorage.setItem('sessionId', sessionId);
    }
    return sessionId;
}

// Add backup ad styles
function addBackupAdStyles() {
    if (document.getElementById('backupAdStyles')) return;
    
    const styles = `
        <style id="backupAdStyles">
            .backup-ad-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .backup-ad-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                backdrop-filter: blur(5px);
            }
            
            .backup-ad-container {
                position: relative;
                background: linear-gradient(135deg, #1e3c72, #2a5298);
                border-radius: 20px;
                padding: 20px;
                max-width: 90%;
                max-height: 90%;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                animation: backupAdSlideIn 0.3s ease-out;
            }
            
            @keyframes backupAdSlideIn {
                from {
                    opacity: 0;
                    transform: scale(0.8) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateY(0);
                }
            }
            
            .backup-ad-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                color: #ffd700;
            }
            
            .backup-ad-header h3 {
                margin: 0;
                font-size: 1.5rem;
            }
            
            .backup-ad-close {
                background: rgba(255, 255, 255, 0.2);
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
            }
            
            .backup-ad-close:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: scale(1.1);
            }
            
            .backup-ad-content {
                text-align: center;
                margin-bottom: 20px;
            }
            
            .backup-ad-media {
                max-width: 100%;
                max-height: 400px;
                border-radius: 10px;
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            }
            
            .backup-ad-description {
                color: #e0e0e0;
                margin: 15px 0 0 0;
                font-size: 1rem;
                line-height: 1.5;
            }
            
            .backup-ad-actions {
                display: flex;
                gap: 10px;
                justify-content: center;
                margin-bottom: 15px;
            }
            
            .backup-ad-btn {
                padding: 12px 24px;
                border: none;
                border-radius: 25px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .backup-ad-btn.primary {
                background: linear-gradient(45deg, #4CAF50, #45a049);
                color: white;
            }
            
            .backup-ad-btn.primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
            }
            
            .backup-ad-btn.secondary {
                background: rgba(255, 255, 255, 0.2);
                color: white;
            }
            
            .backup-ad-btn.secondary:hover {
                background: rgba(255, 255, 255, 0.3);
            }
            
            .backup-ad-timer {
                text-align: center;
                color: #ffd700;
            }
            
            .timer-bar {
                width: 100%;
                height: 4px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 2px;
                overflow: hidden;
                margin-bottom: 8px;
            }
            
            .timer-fill {
                height: 100%;
                background: linear-gradient(90deg, #ffd700, #ffed4e);
                width: 0%;
                transition: width 1s linear;
            }
            
            @media (max-width: 768px) {
                .backup-ad-container {
                    max-width: 95%;
                    padding: 15px;
                }
                
                .backup-ad-header h3 {
                    font-size: 1.2rem;
                }
                
                .backup-ad-media {
                    max-height: 250px;
                }
                
                .backup-ad-actions {
                    flex-direction: column;
                }
            }
        </style>
    `;
    
    document.head.insertAdjacentHTML('beforeend', styles);
}

// Integration with existing mod download system
function integrateWithModDownload() {
    // Override the existing mod download function to include backup ads
    if (window.Android && window.Android.requestModDownloadWithAd) {
        const originalFunction = window.Android.requestModDownloadWithAd;
        
        window.Android.requestModDownloadWithAd = function(modId, modName, downloadUrl) {
            // Try original Unity Ads first
            try {
                originalFunction.call(this, modId, modName, downloadUrl);

                // Set a timeout to show backup ad if Unity Ads fails
                setTimeout(async () => {
                    if (!backupAdDisplayed) {
                        const modData = getCurrentModData(modId);
                        await showBackupAdOnUnityAdsFailure(modId, modData?.category);
                    }
                }, 3000); // Wait 3 seconds for Unity Ads to load

            } catch (error) {
                console.error('Unity Ads failed, showing backup ad:', error);
                // Show backup ad immediately if Unity Ads fails
                const modData = getCurrentModData(modId);
                showBackupAdOnUnityAdsFailure(modId, modData?.category);
            }
        };
    }
}

// Get current mod data (implement based on your app structure)
function getCurrentModData(modId) {
    // This should be implemented based on how mod data is stored in your app
    // For now, return null
    return null;
}

// Initialize backup ads system when page loads
document.addEventListener('DOMContentLoaded', async function() {
    try {
        await initializeBackupAds();
        integrateWithModDownload();
    } catch (error) {
        console.error('❌ فشل في تهيئة نظام الإعلانات الاحتياطية:', error);
        // إعادة المحاولة بعد 5 ثوان
        setTimeout(async () => {
            try {
                await initializeBackupAds();
                integrateWithModDownload();
            } catch (retryError) {
                console.error('❌ فشل في إعادة المحاولة:', retryError);
            }
        }, 5000);
    }
});

// Export functions for global use
window.backupAds = {
    show: showBackupAdOnAdMobFailure,
    close: closeBackupAd,
    initialize: initializeBackupAds,
    enabled: () => backupAdsEnabled,
    setEnabled: (enabled) => { backupAdsEnabled = enabled; }
};
