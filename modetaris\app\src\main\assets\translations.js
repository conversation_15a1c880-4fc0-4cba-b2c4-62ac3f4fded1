// Translation system for Mod Etaris app
class TranslationManager {
    constructor() {
        this.currentLanguage = localStorage.getItem('selectedLanguage') || 'en';
        this.translations = {
            ar: {
                // General UI
                'app_name': 'مود إيتاريس',
                'loading': 'جاري التحميل...',
                'error': 'خطأ',
                'success': 'نجح',
                'cancel': 'إلغاء',
                'ok': 'موافق',
                'close': 'إغلاق',
                'back': 'رجوع',
                'next': 'التالي',
                'previous': 'السابق',
                'save': 'حفظ',
                'delete': 'حذف',
                'edit': 'تعديل',
                'add': 'إضافة',
                'search': 'بحث',
                'filter': 'تصفية',
                'sort': 'ترتيب',
                'settings': 'الإعدادات',

                // Categories
                'all': 'الكل',
                'addons': 'الإضافات',
                'shaders': 'الشيدرز',
                'texture_pack': 'حزم النسيج',
                'maps': 'الخرائط',
                'seeds': 'البذور',
                'news': 'الأخبار',
                'suggested': 'مقترح',
                'free_addons': 'إضافات مجانية',

                // Mod details
                'mod_name': 'اسم المود',
                'description': 'الوصف',
                'category': 'الفئة',
                'version': 'الإصدار',
                'size': 'الحجم',
                'downloads': 'التحميلات',
                'likes': 'الإعجابات',
                'creator': 'المطور',
                'download': 'تحميل',
                'like': 'إعجاب',
                'share': 'مشاركة',
                'no_description': 'لا يوجد وصف متاح.',

                // Sorting options
                'most_liked': 'الأكثر إعجاباً',
                'newest': 'الأحدث',
                'most_downloads': 'الأكثر تحميلاً',
                'oldest': 'الأقدم',
                'see_all': 'عرض الكل',

                // Badges
                'new_badge': 'جديد',
                'popular_badge': 'شائع',
                'free_addon_badge': 'إضافة مجانية',

                // Platform
                'platform': 'المنصة',
                'bedrock': 'بيدروك',
                'java': 'جافا',

                // Shader warning
                'shader_warning_title': '⚠️ تحذير مهم',
                'shader_warning_description': 'جميع الشيدرز تعمل فقط مع إصدار ماين كرافت المُعدّل (Patched) وتطبيق BetterRenderDragon. تأكد من تثبيت هذه المتطلبات قبل تحميل أي شيدر.',
                'shader_warning_understand': 'فهمت',
                'shader_warning_dont_show': 'عدم الإظهار مجدداً',
                'shader_warning_translate_ar': 'عربي',
                'shader_warning_translate_en': 'English',

                // Download messages
                'download_starting': 'بدء التحميل...',
                'download_complete': 'اكتمل التحميل',
                'download_failed': 'فشل التحميل',
                'download_invalid_link': 'رابط التحميل غير صالح!',
                'download_unavailable': 'رابط التحميل غير متاح أو غير صالح!',
                'download_error': 'خطأ في بدء التحميل.',
                'download_fallback': 'لا يمكن بدء التحميل التلقائي. سيتم فتح الرابط في المتصفح.',

                // Error messages
                'error_loading_mods': 'خطأ في تحميل المودات',
                'error_updating_like': 'خطأ في تحديث الإعجاب.',
                'error_unexpected_like': 'خطأ غير متوقع في تحديث الإعجاب.',
                'error_modal_data': 'بيانات غير صالحة لعرض التفاصيل.',
                'error_supabase_init': 'خطأ في تهيئة قاعدة البيانات',

                // Language selection
                'choose_language': 'اختر لغتك',
                'language_subtitle': 'اختر لغتك المفضلة',
                'language_change_later': 'يمكنك تغيير اللغة لاحقاً من الإعدادات',
                'change_language': 'تعديل اللغة',
                'language_changed_success': 'تم تغيير اللغة بنجاح! سيتم إعادة تحميل الصفحة...',
                'arabic': 'العربية',
                'english': 'الإنجليزية',

                // Time and dates
                'created_at': 'تاريخ الإنشاء',
                'updated_at': 'تاريخ التحديث',
                'today': 'اليوم',
                'yesterday': 'أمس',
                'days_ago': 'منذ {0} أيام',
                'weeks_ago': 'منذ {0} أسابيع',
                'months_ago': 'منذ {0} أشهر',

                // File operations
                'file_not_found': 'لم يتم العثور على الملف المحمل. ربما تم حذفه؟',
                'file_access_error': 'خطأ في الوصول إلى الملف.',
                'file_name_error': 'لا يمكن تحديد اسم الملف.',

                // Installation
                'installation_guide': 'دليل التثبيت',
                'installation_instructions': 'تعليمات التثبيت',
                'step': 'الخطوة',

                // Premium features
                'premium_required': 'يتطلب اشتراك مميز',
                'get_premium': 'احصل على المميز',
                'premium_benefits': 'مزايا الاشتراك المميز',

                // Creator info
                'creator_info_title': 'معلومات صانع المود',
                'creator_label': 'صانع المود:',
                'social_label': 'وسائل التواصل الاجتماعي:',
                'copyright_title': 'حقوق الطبع والنشر',
                'copyright_desc': 'نحن نشارك اسم صانع المود ووسائل التواصل الاجتماعي الخاصة به كما هي موجودة في المود المنشور على المواقع الأصلية. يمكن لصانع المود المطالبة بإزالة المود من التطبيق عبر التواصل معنا.',
                'contact_title': 'للتواصل معنا:',
                'contact_info': 'البريد الإلكتروني: <EMAIL>',
                'no_creator': 'غير محدد',
                'no_social': 'لا توجد وسائل تواصل متاحة',
                'close_btn': 'إغلاق',

                // Custom copyright
                'custom_copyright_desc': 'هذا النوع من المودات يتطلب من المالك فرض اختصار روابط أو مشاهدة إعلانات، وأرباحها تذهب إلى المالك مباشرة من خلال الإعلانات التي يشاهدها المستخدم. يمكن للمالك طلب إزالة المود من التطبيق عبر التواصل معنا.',

                // About app
                'about_app': 'عن التطبيق',
                'about_app_title': 'معلومات التطبيق وإخلاء المسؤولية القانونية',
                'app_info_title': 'معلومات التطبيق',
                'app_description': 'Mod Etaris هو تطبيق غير رسمي من طرف ثالث لـ Minecraft Pocket Edition. يوفر التطبيق مجموعة واسعة من المودات والإضافات والشيدرز وحزم النسيج والخرائط والبذور.',
                'legal_disclaimer_title': 'إخلاء مسؤولية قانوني',

                // App Ownership
                'app_ownership': 'ملكية التطبيق',
                'app_ownership_title': 'إثبات ملكية التطبيق - Mod Etaris',
                'ownership_verification_title': 'تأكيد الملكية',
                'ownership_verification_text': 'هذا التطبيق "Mod Etaris" مملوك بالكامل ومطور من قبل Sidi Mohamed. جميع الحقوق محفوظة للمطور الأصلي.',
                'developer_info_title': 'معلومات المطور',
                'developer_name': 'اسم المطور: Sidi Mohamed',
                'developer_role': 'المنصب: مطور تطبيقات الهاتف المحمول ومطور ألعاب',
                'developer_experience': 'الخبرة: متخصص في تطوير تطبيقات Android وتطبيقات Minecraft',
                'app_creation_date': 'تاريخ إنشاء التطبيق: 2024',
                'app_platform': 'المنصة: Android (Google Play Store & OPPO App Market)',
                'technical_details_title': 'التفاصيل التقنية',
                'app_package_name': 'اسم الحزمة: com.sidimohamed.modetaris',
                'app_version_info': 'إصدار التطبيق: يتم تحديثه بانتظام',
                'app_architecture': 'البنية: Android Native (Kotlin/Java) + WebView',
                'database_system': 'نظام قاعدة البيانات: Supabase PostgreSQL',
                'legal_ownership_title': 'الملكية القانونية',
                'legal_ownership_text': 'يؤكد Sidi Mohamed أنه المالك الوحيد والمطور الأصلي لتطبيق Mod Etaris. التطبيق مسجل تحت اسمه في جميع المتاجر الرسمية.',
                'intellectual_property_title': 'الملكية الفكرية',
                'intellectual_property_text': 'جميع عناصر التصميم، الكود المصدري، واجهة المستخدم، والوظائف الأساسية للتطبيق هي من إبداع وتطوير Sidi Mohamed حصرياً.',
                'contact_verification_title': 'للتحقق من الملكية',
                'contact_verification_text': 'للتحقق من ملكية التطبيق أو لأي استفسارات رسمية، يرجى التواصل مع المطور مباشرة عبر: <EMAIL>',
                'legal_disclaimer_text': 'إخلاء مسؤولية قانوني: هذا تطبيق غير رسمي من طرف ثالث لـ Minecraft Pocket Edition. هذا التطبيق غير معتمد أو تابع أو مرتبط بـ Mojang AB أو Microsoft بأي شكل من الأشكال. اسم "Minecraft" وعلامة Minecraft التجارية وأصول Minecraft كلها ملك لـ Mojang AB أو أصحابها المعنيين. جميع الحقوق محفوظة. هذا التطبيق هو مشروع من صنع المعجبين، تم إنشاؤه للمعجبين من قبل المعجبين، ولا يبيع أي ملفات لعبة معدلة أو إضافات.',
                'copyright_info_title': 'حقوق النشر والملكية',
                'copyright_info_text': 'هذا التطبيق، "Mod Etaris"، ومحتواه الأصلي وتصميمه ووظائفه هي ملكية حصرية لفريق تطوير Mod Etaris. جميع المودات والإضافات ومحتويات الطرف الثالث الأخرى المعروضة داخل التطبيق هي ملك فكري لمبدعيها. نحن نحترم حقوق الملكية الفكرية للآخرين بشدة ولا ندعي أي ملكية على المودات المقدمة.',
                'contact_support_title': 'للمزيد من المعلومات والاستفسارات',
                'contact_support_text': 'لمزيد من المعلومات والاستفسارات، يرجى التواصل معنا عبر: <EMAIL>'
            },
            en: {
                // General UI
                'app_name': 'Mod Etaris',
                'loading': 'Loading...',
                'error': 'Error',
                'success': 'Success',
                'cancel': 'Cancel',
                'ok': 'OK',
                'close': 'Close',
                'back': 'Back',
                'next': 'Next',
                'previous': 'Previous',
                'save': 'Save',
                'delete': 'Delete',
                'edit': 'Edit',
                'add': 'Add',
                'search': 'Search',
                'filter': 'Filter',
                'sort': 'Sort',
                'settings': 'Settings',

                // Categories
                'all': 'All',
                'addons': 'Addons',
                'shaders': 'Shaders',
                'texture_pack': 'Texture Packs',
                'maps': 'Maps',
                'seeds': 'Seeds',
                'news': 'News',
                'suggested': 'Suggested',
                'free_addons': 'Free Addons',

                // Mod details
                'mod_name': 'Mod Name',
                'description': 'Description',
                'category': 'Category',
                'version': 'Version',
                'size': 'Size',
                'downloads': 'Downloads',
                'likes': 'Likes',
                'creator': 'Creator',
                'download': 'Download',
                'like': 'Like',
                'share': 'Share',
                'no_description': 'No description available.',

                // Sorting options
                'most_liked': 'Most Liked',
                'newest': 'Newest',
                'most_downloads': 'Most Downloads',
                'oldest': 'Oldest',
                'see_all': 'See All',

                // Badges
                'new_badge': 'NEW',
                'popular_badge': 'Popular',
                'free_addon_badge': 'Free Addon',

                // Platform
                'platform': 'Platform',
                'bedrock': 'Bedrock',
                'java': 'Java',

                // Shader warning
                'shader_warning_title': '⚠️ Important Warning',
                'shader_warning_description': 'All shaders only work with Minecraft Patched version and BetterRenderDragon app. Make sure to install these requirements before downloading any shader.',
                'shader_warning_understand': 'I Understand',
                'shader_warning_dont_show': 'Don\'t Show Again',
                'shader_warning_translate_ar': 'عربي',
                'shader_warning_translate_en': 'English',

                // Download messages
                'download_starting': 'Starting download...',
                'download_complete': 'Download complete',
                'download_failed': 'Download failed',
                'download_invalid_link': 'Download link is unavailable or invalid!',
                'download_unavailable': 'Download link is unavailable or invalid!',
                'download_error': 'Error starting download.',
                'download_fallback': 'Automatic download cannot be started. The link will be opened in the browser.',

                // Error messages
                'error_loading_mods': 'Error loading mods',
                'error_updating_like': 'Error updating like.',
                'error_unexpected_like': 'Unexpected error updating like.',
                'error_modal_data': 'Invalid item data for modal.',
                'error_supabase_init': 'Error initializing database',

                // Language selection
                'choose_language': 'Choose Your Language',
                'language_subtitle': 'Choose your preferred language',
                'language_change_later': 'You can change the language later from settings',
                'change_language': 'Change Language',
                'language_changed_success': 'Language changed successfully! Page will reload...',
                'arabic': 'Arabic',
                'english': 'English',

                // Time and dates
                'created_at': 'Created At',
                'updated_at': 'Updated At',
                'today': 'Today',
                'yesterday': 'Yesterday',
                'days_ago': '{0} days ago',
                'weeks_ago': '{0} weeks ago',
                'months_ago': '{0} months ago',

                // File operations
                'file_not_found': 'Downloaded file not found. It may have been deleted.',
                'file_access_error': 'Error accessing file.',
                'file_name_error': 'Cannot determine file name.',

                // Installation
                'installation_guide': 'Installation Guide',
                'installation_instructions': 'Installation Instructions',
                'step': 'Step',

                // Premium features
                'premium_required': 'Premium subscription required',
                'get_premium': 'Get Premium',
                'premium_benefits': 'Premium Benefits',

                // Creator info
                'creator_info_title': 'Mod Creator Information',
                'creator_label': 'Mod Creator:',
                'social_label': 'Social Media:',
                'copyright_title': 'Copyright Information',
                'copyright_desc': 'We share the mod creator\'s name and social media links as they appear in the original mod published on official websites. The mod creator can request removal of their mod from our app by contacting us.',
                'contact_title': 'Contact Us:',
                'contact_info': 'Email: <EMAIL>',
                'no_creator': 'Not specified',
                'no_social': 'No social media available',
                'close_btn': 'Close',

                // Custom copyright
                'custom_copyright_desc': 'This type of mod requires the owner to impose link shortening or ad viewing, and the profits go directly to the owner through the ads viewed by the user. The owner can request removal of the mod from the app by contacting us.',

                // About app
                'about_app': 'About App',
                'about_app_title': 'App Information & Legal Disclaimer',
                'app_info_title': 'App Information',
                'app_description': 'Mod Etaris is an unofficial third-party application for Minecraft Pocket Edition. The app provides a wide range of mods, addons, shaders, texture packs, maps, and seeds.',
                'legal_disclaimer_title': 'Legal Disclaimer',

                // App Ownership
                'app_ownership': 'App Ownership',
                'app_ownership_title': 'App Ownership Verification - Mod Etaris',
                'ownership_verification_title': 'Ownership Confirmation',
                'ownership_verification_text': 'This application "Mod Etaris" is fully owned and developed by Sidi Mohamed. All rights are reserved to the original developer.',
                'developer_info_title': 'Developer Information',
                'developer_name': 'Developer Name: Sidi Mohamed',
                'developer_role': 'Position: Mobile App Developer & Game Developer',
                'developer_experience': 'Experience: Specialized in Android app development and Minecraft applications',
                'app_creation_date': 'App Creation Date: 2024',
                'app_platform': 'Platform: Android (Google Play Store & OPPO App Market)',
                'technical_details_title': 'Technical Details',
                'app_package_name': 'Package Name: com.sidimohamed.modetaris',
                'app_version_info': 'App Version: Regularly updated',
                'app_architecture': 'Architecture: Android Native (Kotlin/Java) + WebView',
                'database_system': 'Database System: Supabase PostgreSQL',
                'legal_ownership_title': 'Legal Ownership',
                'legal_ownership_text': 'Sidi Mohamed confirms that he is the sole owner and original developer of the Mod Etaris application. The app is registered under his name in all official stores.',
                'intellectual_property_title': 'Intellectual Property',
                'intellectual_property_text': 'All design elements, source code, user interface, and core functionalities of the application are exclusively created and developed by Sidi Mohamed.',
                'contact_verification_title': 'For Ownership Verification',
                'contact_verification_text': 'To verify app ownership or for any official inquiries, please contact the developer directly at: <EMAIL>',
                'legal_disclaimer_text': 'LEGAL DISCLAIMER: This is an unofficial third-party application for Minecraft Pocket Edition. This application is not approved, affiliated, or associated with Mojang AB or Microsoft in any way. The "Minecraft" name, the Minecraft brand, and the Minecraft assets are all property of Mojang AB or their respective owners. All rights are reserved. This application is a fan-made project, created for fans by fans, and does not sell any modified game files or addons.',
                'copyright_info_title': 'Copyright & Ownership',
                'copyright_info_text': 'This application, "Mod Etaris", and its original content, design, and functionality are the exclusive property of the Mod Etaris development team. All mods, addons, and other third-party content featured within the app are the intellectual property of their respective creators. We deeply respect the intellectual property rights of others and do not claim any ownership over the mods provided.',
                'contact_support_title': 'For More Information & Inquiries',
                'contact_support_text': 'For more information and inquiries, please contact us at: <EMAIL>'
            }
        };
    }

    // Get translation for a key
    t(key, ...args) {
        const translation = this.translations[this.currentLanguage]?.[key] ||
                          this.translations['en'][key] ||
                          key;

        // Replace placeholders {0}, {1}, etc. with arguments
        return translation.replace(/\{(\d+)\}/g, (match, index) => {
            return args[index] !== undefined ? args[index] : match;
        });
    }

    // Set current language
    setLanguage(language) {
        if (this.translations[language]) {
            this.currentLanguage = language;
            localStorage.setItem('selectedLanguage', language);

            // Update document direction and language
            if (language === 'ar') {
                document.documentElement.dir = 'rtl';
                document.documentElement.lang = 'ar';
            } else {
                document.documentElement.dir = 'ltr';
                document.documentElement.lang = 'en';
            }

            // Trigger language change event
            window.dispatchEvent(new CustomEvent('languageChanged', {
                detail: { language: language }
            }));
        }
    }

    // Get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Check if current language is RTL
    isRTL() {
        return this.currentLanguage === 'ar';
    }

    // Initialize translation system
    init() {
        const savedLanguage = localStorage.getItem('selectedLanguage');
        if (savedLanguage && this.translations[savedLanguage]) {
            this.setLanguage(savedLanguage);
        }
    }
}

// Create global translation manager instance
window.translationManager = new TranslationManager();

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
    window.translationManager.init();
});

// Helper function for easy access to translations
window.t = (key, ...args) => window.translationManager.t(key, ...args);
