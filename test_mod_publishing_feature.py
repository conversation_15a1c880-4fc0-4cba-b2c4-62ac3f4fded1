#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة نشر المود التجريبي
"""

import os
import sys
import json
import tempfile
import zipfile
from datetime import datetime

def create_test_mod_file():
    """إنشاء ملف مود تجريبي للاختبار"""
    try:
        # إنشاء مجلد مؤقت
        temp_dir = tempfile.mkdtemp()
        
        # إنشاء ملف manifest.json
        manifest = {
            "format_version": 2,
            "header": {
                "description": "Test Mod for Development",
                "name": "Test Mod",
                "uuid": "12345678-1234-1234-1234-123456789012",
                "version": [1, 0, 0],
                "min_engine_version": [1, 16, 0]
            },
            "modules": [
                {
                    "description": "Test Module",
                    "type": "data",
                    "uuid": "*************-4321-4321-************",
                    "version": [1, 0, 0]
                }
            ]
        }
        
        manifest_path = os.path.join(temp_dir, "manifest.json")
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2)
        
        # إنشاء ملف pack_icon.png (ملف فارغ)
        icon_path = os.path.join(temp_dir, "pack_icon.png")
        with open(icon_path, 'wb') as f:
            # كتابة بيانات PNG بسيطة (1x1 pixel)
            png_data = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\x0cIDATx\x9cc```\x00\x00\x00\x04\x00\x01\xdd\x8d\xb4\x1c\x00\x00\x00\x00IEND\xaeB`\x82'
            f.write(png_data)
        
        # إنشاء ملف ZIP
        downloads_path = os.path.join(os.path.expanduser("~"), "Downloads")
        if not os.path.exists(downloads_path):
            os.makedirs(downloads_path)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"test_mod_{timestamp}.mcpack"
        zip_path = os.path.join(downloads_path, zip_filename)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            zipf.write(manifest_path, "manifest.json")
            zipf.write(icon_path, "pack_icon.png")
        
        print(f"✅ تم إنشاء ملف مود تجريبي: {zip_path}")
        return zip_path
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف المود التجريبي: {e}")
        return None

def test_get_latest_mod_file():
    """اختبار دالة جلب آخر ملف مود"""
    print("🧪 اختبار جلب آخر ملف مود...")
    
    # إنشاء ملف مود تجريبي
    test_file = create_test_mod_file()
    if not test_file:
        print("❌ فشل في إنشاء ملف المود التجريبي")
        return False
    
    # محاكاة دالة جلب آخر ملف
    downloads_path = os.path.join(os.path.expanduser("~"), "Downloads")
    mod_extensions = ['.mcpack', '.mcaddon', '.mcworld', '.zip']
    mod_files = []
    
    for file in os.listdir(downloads_path):
        if any(file.lower().endswith(ext) for ext in mod_extensions):
            file_path = os.path.join(downloads_path, file)
            mod_files.append((file_path, os.path.getmtime(file_path)))
    
    if mod_files:
        mod_files.sort(key=lambda x: x[1], reverse=True)
        latest_file = mod_files[0][0]
        print(f"✅ تم العثور على آخر ملف: {os.path.basename(latest_file)}")
        return True
    else:
        print("❌ لم يتم العثور على ملفات مود")
        return False

def test_default_images():
    """اختبار الصور الافتراضية"""
    print("🧪 اختبار الصور الافتراضية...")
    
    default_images = {
        'primary': [
            "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/test_images%2Ftest_primary_1.jpg?alt=media",
            "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/test_images%2Ftest_primary_2.jpg?alt=media",
            "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/test_images%2Ftest_primary_3.jpg?alt=media"
        ],
        'additional': [
            "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/test_images%2Ftest_additional_1.jpg?alt=media",
            "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/test_images%2Ftest_additional_2.jpg?alt=media",
            "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/test_images%2Ftest_additional_3.jpg?alt=media",
            "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/test_images%2Ftest_additional_4.jpg?alt=media",
            "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/test_images%2Ftest_additional_5.jpg?alt=media"
        ]
    }
    
    if len(default_images['primary']) >= 3 and len(default_images['additional']) >= 5:
        print("✅ الصور الافتراضية متوفرة")
        return True
    else:
        print("❌ الصور الافتراضية غير كاملة")
        return False

def test_mod_data_generation():
    """اختبار إنشاء بيانات المود"""
    print("🧪 اختبار إنشاء بيانات المود...")
    
    import random
    
    test_names = [
        "Test Mod Alpha", "Beta Testing Mod", "Experimental Pack", 
        "Debug Addon", "Sample Modification", "Trial Version",
        "Testing Suite", "Development Build", "Preview Pack"
    ]
    
    test_descriptions = [
        "This is a test mod for development and debugging purposes. Contains experimental features and may not be stable.",
        "Beta version of a new modification. Use for testing only. Report any bugs or issues found.",
        "Experimental addon with new features. This is a development build for testing purposes.",
        "Sample modification for demonstration. Not intended for production use.",
        "Testing pack with various experimental elements. Use at your own risk."
    ]
    
    categories = ["Addons", "Texture Packs", "Maps", "Skins", "Tools"]
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    test_data = {
        'name': f"{random.choice(test_names)} {timestamp}",
        'description': random.choice(test_descriptions),
        'arabic_description': "هذا مود تجريبي للاختبار والتطوير. يحتوي على ميزات تجريبية وقد لا يكون مستقراً.",
        'category': random.choice(categories),
        'version': f"Test-{random.randint(1, 999)}",
        'creator_name': "Test Developer",
        'creator_contact': "<EMAIL>",
        'telegram_desc_en': f"🧪 Test Mod - {timestamp}\n\nExperimental features included. Use for testing only.",
        'telegram_desc_ar': f"🧪 مود تجريبي - {timestamp}\n\nيحتوي على ميزات تجريبية. للاختبار فقط."
    }
    
    required_fields = ['name', 'description', 'category', 'version', 'creator_name']
    
    for field in required_fields:
        if not test_data.get(field):
            print(f"❌ حقل مطلوب مفقود: {field}")
            return False
    
    print("✅ تم إنشاء بيانات المود بنجاح")
    print(f"   الاسم: {test_data['name']}")
    print(f"   الفئة: {test_data['category']}")
    print(f"   الإصدار: {test_data['version']}")
    return True

def test_settings_functionality():
    """اختبار وظائف الإعدادات"""
    print("🧪 اختبار وظائف الإعدادات...")
    
    # إنشاء إعدادات تجريبية
    test_settings = {
        'additional_images_count': 4,
        'default_creator': 'Test Developer',
        'default_contact': '<EMAIL>',
        'default_category': 'Addons'
    }
    
    try:
        # حفظ الإعدادات
        with open('test_mod_settings.json', 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, ensure_ascii=False, indent=2)
        
        # قراءة الإعدادات
        with open('test_mod_settings.json', 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
        
        # التحقق من صحة البيانات
        if loaded_settings == test_settings:
            print("✅ وظائف الإعدادات تعمل بشكل صحيح")
            
            # حذف ملف الاختبار
            os.remove('test_mod_settings.json')
            return True
        else:
            print("❌ خطأ في حفظ/قراءة الإعدادات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار ميزة نشر المود التجريبي")
    print("=" * 50)
    
    tests = [
        ("جلب آخر ملف مود", test_get_latest_mod_file),
        ("الصور الافتراضية", test_default_images),
        ("إنشاء بيانات المود", test_mod_data_generation),
        ("وظائف الإعدادات", test_settings_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! ميزة نشر المود التجريبي جاهزة للاستخدام.")
        print("\n✨ الميزات المتاحة:")
        print("   🚀 نشر مود تجريبي بضغطة زر واحد")
        print("   📁 جلب آخر ملف مود من التنزيلات تلقائياً")
        print("   🖼️ استخدام صور افتراضية جاهزة")
        print("   📝 إنشاء بيانات مود تجريبية تلقائياً")
        print("   ⚙️ إعدادات قابلة للتخصيص")
        print("   👁️ عرض آخر مود تجريبي")
        print("   🗑️ حذف جميع المودات التجريبية")
        
        return True
    else:
        print("❌ بعض الاختبارات فشلت. راجع التفاصيل أعلاه.")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار ميزة نشر المود التجريبي")
    print("تاريخ الاختبار:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    success = run_all_tests()
    
    if success:
        print("\n🎯 الميزة جاهزة للاستخدام في التطبيق الرئيسي!")
        return 0
    else:
        print("\n⚠️ تحتاج الميزة إلى مراجعة إضافية.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
