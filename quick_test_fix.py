#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لإصلاح مشكلة supabase_client
"""

import os
import sys

def test_imports():
    """اختبار الاستيرادات"""
    try:
        print("🧪 اختبار الاستيرادات...")
        
        # اختبار استيراد الوحدات الأساسية
        from datetime import datetime
        import random
        import json
        import tempfile
        import zipfile
        
        print("✅ الوحدات الأساسية: تم استيرادها بنجاح")
        
        # اختبار استيراد Supabase
        try:
            from supabase import create_client, Client
            print("✅ Supabase: متاح")
        except ImportError:
            print("❌ Supabase: غير متاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيرادات: {e}")
        return False

def test_function_definitions():
    """اختبار تعريف الدوال"""
    try:
        print("\n🧪 اختبار تعريف الدوال...")
        
        # قراءة الملف الرئيسي
        with open('mod_processor_broken_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # قائمة الدوال المطلوبة
        required_functions = [
            'get_latest_mod_file_from_downloads',
            'get_default_test_images',
            'generate_test_mod_data',
            'handle_publish_test_mod',
            'publish_test_mod_worker',
            'upload_mod_file_to_firebase',
            'publish_mod_to_database',
            'show_test_mod_settings',
            'view_last_test_mod',
            'delete_all_test_mods'
        ]
        
        missing_functions = []
        for func in required_functions:
            if f"def {func}(" not in content:
                missing_functions.append(func)
        
        if missing_functions:
            print(f"❌ دوال مفقودة: {missing_functions}")
            return False
        else:
            print("✅ جميع الدوال المطلوبة موجودة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال: {e}")
        return False

def test_variable_references():
    """اختبار مراجع المتغيرات"""
    try:
        print("\n🧪 اختبار مراجع المتغيرات...")
        
        # قراءة الملف الرئيسي
        with open('mod_processor_broken_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من عدم وجود مراجع خاطئة
        problematic_refs = [
            'supabase_client',  # يجب أن يكون app_db_client
        ]
        
        issues = []
        for ref in problematic_refs:
            if ref in content:
                issues.append(ref)
        
        if issues:
            print(f"❌ مراجع متغيرات خاطئة: {issues}")
            return False
        else:
            print("✅ جميع مراجع المتغيرات صحيحة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المتغيرات: {e}")
        return False

def test_ui_elements():
    """اختبار عناصر واجهة المستخدم"""
    try:
        print("\n🧪 اختبار عناصر واجهة المستخدم...")
        
        # قراءة الملف الرئيسي
        with open('mod_processor_broken_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود عناصر الواجهة
        ui_elements = [
            'publish_test_mod_button',
            'test_mod_settings_button',
            'view_last_test_mod_button',
            'delete_test_mods_button',
            'test_mod_frame'
        ]
        
        missing_elements = []
        for element in ui_elements:
            if element not in content:
                missing_elements.append(element)
        
        if missing_elements:
            print(f"❌ عناصر واجهة مفقودة: {missing_elements}")
            return False
        else:
            print("✅ جميع عناصر الواجهة موجودة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        return False

def test_syntax_check():
    """اختبار صحة بناء الجملة"""
    try:
        print("\n🧪 اختبار صحة بناء الجملة...")
        
        # محاولة تجميع الملف
        with open('mod_processor_broken_final.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        compile(content, 'mod_processor_broken_final.py', 'exec')
        print("✅ بناء الجملة صحيح")
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ في بناء الجملة: {e}")
        print(f"   السطر {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ خطأ في التجميع: {e}")
        return False

def run_quick_tests():
    """تشغيل الاختبارات السريعة"""
    print("🚀 بدء الاختبارات السريعة لإصلاح مشكلة supabase_client")
    print("=" * 60)
    
    tests = [
        ("الاستيرادات", test_imports),
        ("تعريف الدوال", test_function_definitions),
        ("مراجع المتغيرات", test_variable_references),
        ("عناصر الواجهة", test_ui_elements),
        ("صحة بناء الجملة", test_syntax_check)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! المشكلة تم إصلاحها.")
        print("\n✨ الميزة جاهزة للاستخدام:")
        print("   🚀 نشر مود تجريبي بضغطة زر واحد")
        print("   📁 جلب آخر ملف من التنزيلات")
        print("   🖼️ صور افتراضية جاهزة")
        print("   ⚙️ إعدادات قابلة للتخصيص")
        
        return True
    else:
        print("❌ بعض الاختبارات فشلت. راجع التفاصيل أعلاه.")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار إصلاح مشكلة supabase_client")
    print("تاريخ الاختبار:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    success = run_quick_tests()
    
    if success:
        print("\n🎯 الإصلاح تم بنجاح! يمكن استخدام الميزة الآن.")
        return 0
    else:
        print("\n⚠️ تحتاج بعض الأمور إلى مراجعة إضافية.")
        return 1

if __name__ == "__main__":
    from datetime import datetime
    exit_code = main()
    sys.exit(exit_code)
