#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة اختبار روابط التحميل للتأكد من توافقها مع تطبيق Modetaris
"""

import requests
import sys
import os
from urllib.parse import urlparse, quote

def test_download_link(url, description=""):
    """اختبار رابط تحميل معين"""
    print(f"\n🔍 اختبار {description}: {url}")
    
    try:
        # اختبار HEAD request أولاً
        response = requests.head(url, timeout=15, allow_redirects=True)
        print(f"   📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            # فحص headers
            content_type = response.headers.get('content-type', 'unknown')
            content_length = response.headers.get('content-length', 'unknown')
            content_disposition = response.headers.get('content-disposition', 'none')
            
            print(f"   📋 Content-Type: {content_type}")
            print(f"   📏 Content-Length: {content_length}")
            print(f"   📎 Content-Disposition: {content_disposition}")
            
            # فحص إذا كان الرابط يدعم التحميل المباشر
            if 'application/octet-stream' in content_type or 'application/zip' in content_type or content_type == 'application/octet-stream':
                print(f"   ✅ نوع المحتوى مناسب للتحميل")
            else:
                print(f"   ⚠️ نوع المحتوى قد لا يكون مناسب: {content_type}")
            
            # فحص الحجم
            if content_length != 'unknown':
                try:
                    size_bytes = int(content_length)
                    size_mb = size_bytes / (1024 * 1024)
                    print(f"   📦 حجم الملف: {size_mb:.2f} MB")
                    
                    if size_mb > 0.1:  # أكبر من 100KB
                        print(f"   ✅ حجم الملف معقول")
                    else:
                        print(f"   ⚠️ حجم الملف صغير جداً")
                except:
                    print(f"   ⚠️ لا يمكن تحليل حجم الملف")
            
            # اختبار تحميل جزء صغير للتأكد
            try:
                partial_response = requests.get(url, headers={'Range': 'bytes=0-1023'}, timeout=10)
                if partial_response.status_code in [200, 206]:
                    print(f"   ✅ الرابط يدعم التحميل الجزئي")
                    
                    # فحص بداية الملف
                    content_start = partial_response.content[:10]
                    if content_start.startswith(b'PK'):
                        print(f"   ✅ الملف يبدو كملف ZIP/MCPACK صحيح")
                    else:
                        print(f"   ⚠️ الملف قد لا يكون ZIP/MCPACK: {content_start}")
                else:
                    print(f"   ⚠️ التحميل الجزئي غير مدعوم: {partial_response.status_code}")
            except Exception as e:
                print(f"   ⚠️ خطأ في اختبار التحميل الجزئي: {e}")
            
            return True
        else:
            print(f"   ❌ فشل الوصول للرابط: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"   ❌ انتهت مهلة الاتصال")
        return False
    except requests.exceptions.ConnectionError:
        print(f"   ❌ خطأ في الاتصال")
        return False
    except Exception as e:
        print(f"   ❌ خطأ غير متوقع: {e}")
        return False

def generate_alternative_urls(base_url):
    """إنشاء روابط بديلة لنفس الملف"""
    alternatives = []
    
    if "firebasestorage.googleapis.com" in base_url:
        # استخراج معلومات الملف من رابط Firebase
        try:
            # مثال: https://firebasestorage.googleapis.com/v0/b/bucket/o/path%2Ffile.mcpack?alt=media
            parts = base_url.split('/o/')
            if len(parts) == 2:
                bucket_part = parts[0].replace('https://firebasestorage.googleapis.com/v0/b/', '')
                file_part = parts[1].split('?')[0]
                
                # فك التشفير
                from urllib.parse import unquote
                file_path = unquote(file_part)
                
                # إنشاء روابط بديلة
                alternatives.extend([
                    # رابط storage.googleapis.com مباشر
                    f"https://storage.googleapis.com/{bucket_part}/{file_path}",
                    # رابط Firebase بدون encoding
                    f"https://firebasestorage.googleapis.com/v0/b/{bucket_part}/o/{file_path}?alt=media",
                    # رابط Firebase مع token
                    f"https://firebasestorage.googleapis.com/v0/b/{bucket_part}/o/{file_part}?alt=media&token=public",
                ])
        except Exception as e:
            print(f"   ⚠️ خطأ في إنشاء روابط Firebase البديلة: {e}")
    
    elif "supabase.co" in base_url:
        # روابط Supabase عادة تعمل كما هي
        alternatives.append(base_url)
    
    return alternatives

def main():
    """الدالة الرئيسية"""
    print("🚀 أداة اختبار روابط التحميل لتطبيق Modetaris")
    print("=" * 60)
    
    # روابط للاختبار
    test_urls = [
        {
            "url": "https://firebasestorage.googleapis.com/v0/b/download-e33a2.firebasestorage.app/o/mods%2FGlow-Shot_1754214663_6zpf42g4.mcpack?alt=media",
            "description": "رابط Firebase الأصلي"
        },
        {
            "url": "https://storage.googleapis.com/download-e33a2.firebasestorage.app/mods/Glow-Shot_1754214663_6zpf42g4.mcpack",
            "description": "رابط Firebase مباشر"
        }
    ]
    
    # إضافة روابط من المستخدم إذا أراد
    if len(sys.argv) > 1:
        user_url = sys.argv[1]
        test_urls.append({
            "url": user_url,
            "description": "رابط من المستخدم"
        })
    
    successful_tests = 0
    total_tests = 0
    
    for test_case in test_urls:
        total_tests += 1
        if test_download_link(test_case["url"], test_case["description"]):
            successful_tests += 1
        
        # اختبار روابط بديلة
        alternatives = generate_alternative_urls(test_case["url"])
        for i, alt_url in enumerate(alternatives):
            if alt_url != test_case["url"]:  # تجنب تكرار نفس الرابط
                total_tests += 1
                if test_download_link(alt_url, f"بديل {i+1}"):
                    successful_tests += 1
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {successful_tests}/{total_tests} روابط تعمل")
    
    if successful_tests > 0:
        print("\n💡 توصيات لحل مشكلة تطبيق Modetaris:")
        print("1. تأكد من أن التطبيق يدعم روابط Firebase")
        print("2. جرب استخدام روابط Supabase كبديل")
        print("3. تحقق من إعدادات CORS في Firebase")
        print("4. تأكد من أن الملف لم يتم حذفه من التخزين")
        print("5. جرب تحديث التطبيق إلى أحدث إصدار")
    else:
        print("\n❌ جميع الروابط فشلت. قد تكون هناك مشكلة في:")
        print("- إعدادات Firebase")
        print("- الاتصال بالإنترنت")
        print("- صلاحيات الوصول للملفات")
    
    return successful_tests > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
